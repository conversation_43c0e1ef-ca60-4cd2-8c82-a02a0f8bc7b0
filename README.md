# 环境说明
## python 环境
python 版本 3.10.x
执行下面命令：
```
python3 -m venv .venv
source .venv/bin/activate
pip3 install -r requirements.txt
```

# API 使用说明

## 守护进程架构
CodeMind 现在使用守护进程架构，提供更高效的性能和更好的资源管理。

## 守护进程管理

### 启动守护进程
```bash
python codemind_api.py --repo-path /path/to/your/repo start
```

### 查看守护进程状态
```bash
python codemind_api.py --repo-path /path/to/your/repo status
```

### 停止守护进程
```bash
python codemind_api.py --repo-path /path/to/your/repo stop
```

### 测试API功能
```bash
# 测试索引构建
python codemind_api.py --repo-path /path/to/your/repo test --action index

# 测试代码库搜索
python codemind_api.py --repo-path /path/to/your/repo test --action search --query "your query"

# 测试grep搜索
python codemind_api.py --repo-path /path/to/your/repo test --action grep --query "your query"

# 测试知识库搜索
python codemind_api.py --repo-path /path/to/your/repo test --action knowledge --query "your query"

# 测试扩展源搜索
python codemind_api.py --repo-path /path/to/your/repo test --action extension --query "your query"

# 查看缓存目录
python codemind_api.py --repo-path /path/to/your/repo test --action cache
```

## Python 客户端 API

### 基本使用
```python
from codemind_api import CodeMindClient

# 创建客户端实例
client = CodeMindClient("/path/to/your/repo")

# 客户端会自动管理守护进程的启动和通信
```

### 索引代码库
```python
# 基本索引
result = client.index_codebase()

# 强制重新索引
result = client.index_codebase(force_reindex=True)

# 包含扩展源和LLM摘要
result = client.index_codebase(
    extension_sources='["source1", "source2"]'
)
```

### 代码库搜索
```python
# 基本搜索
result = client.codebase_search("your search query")

# 高级搜索选项
result = client.codebase_search(
    query="your search query",
    limit=20,
    min_score=0
)
```

### Grep搜索
```python
result = client.grep_search(
    query="your grep query",
    context_lines=5,
    limit=10
)
```

### 知识库搜索
```python
result = client.knowledge_search(
    query="your search query",
    limit=3,
    timeout=30
)
```

### 扩展源搜索
索引代码库时指定了`extension_sources`才生效
```python
result = client.extension_search(
    query="your search query",
    limit=5,
    use_rerank=False,
    min_score=0.5
)
```

### 获取缓存目录
```python
result = client.get_cache_directory()
print(result['data']['cache_directory'])
```

### 关闭守护进程
```python
result = client.shutdown_daemon()
```

# 打包
1. 更新package.json version
2. https://yunxiao.devops.xiaohongshu.com/ci/project/53851/detail?repoName=transagent/codemind 触发pipeline