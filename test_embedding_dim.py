#!/usr/bin/env python3
"""
测试embedding API的维度
"""
import requests
import json
from config.server import CODEWIZ_RAG_URL, LLM_API_KEY

def test_embedding_dimension():
    """测试embedding API返回的向量维度"""
    headers = {
        'Content-Type': 'application/json',
        'api-key': LLM_API_KEY
    }
    
    payload = json.dumps({
        "model": "Qwen3-Embedding-0.6B",
        "input": "test content",
        "encoding_format": "float"
    })
    
    try:
        response = requests.post(CODEWIZ_RAG_URL, headers=headers, data=payload, timeout=30)
        response.raise_for_status()
        
        result = response.json()
        embedding = result.get("data", [])[0].get("embedding", [])
        
        print(f"Embedding API URL: {CODEWIZ_RAG_URL}")
        print(f"Model: Qwen3-Embedding-0.6B")
        print(f"Embedding dimension: {len(embedding)}")
        print(f"First 5 values: {embedding[:5]}")
        
        return len(embedding)
        
    except Exception as e:
        print(f"Error testing embedding: {e}")
        return None

if __name__ == "__main__":
    test_embedding_dimension()
