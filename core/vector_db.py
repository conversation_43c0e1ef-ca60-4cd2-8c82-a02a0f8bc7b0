import json
import multiprocessing
import os
from typing import Generator

import backoff
from diskcache import Cache
import numpy as np
import openai
import requests
from loguru import logger
from scipy.spatial.distance import cdist

from tqdm import tqdm
import voyageai
from voyageai import error as voyageai_error

try:
    import boto3
    from botocore.exceptions import ClientError
except ImportError:
    boto3 = None
    ClientError = Exception

from utils.timer import Timer
from config.server import (
    BATCH_SIZE, get_cache_directory, VOYAGE_API_AWS_ENDPOINT_NAME, VOYAGE_API_KEY,
    VOYAGE_API_USE_AWS, CODEWIZ_RAG_URL, CODEWIZ_RAG_ENABLED,
    VOYAGE_API_AWS_ACCESS_KEY, VOYAGE_API_AWS_SECRET_KEY, VOYAGE_API_AWS_REGION,
    LLM_API_KEY
)
from utils.hash import hash_sha256
from utils.openai_proxy import get_embeddings_client
from utils.tiktoken_utils import Tiktoken

from config.cache_constants import CacheType, normalize_cache_type, UNIFIED_CACHE_VERSION

# Now uses Voyage AI or CodeWiz RAG if available, with asymmetric embedding
# 使用统一缓存版本，但保留服务类型后缀以区分不同的embedding服务
if CODEWIZ_RAG_ENABLED:
    suffix = "-codewiz-rag"
else:
    suffix = "-voyage-aws" if VOYAGE_API_USE_AWS else "-voyage" if VOYAGE_API_KEY else ""
CACHE_VERSION = UNIFIED_CACHE_VERSION + suffix
tiktoken_client = Tiktoken()

# 使用统一缓存管理器
from utils.cache_manager import get_vector_cache

# 不再使用全局变量，避免在模块导入时初始化缓存
# 所有缓存访问都通过函数调用进行


def cosine_similarity(a, B):
    # use scipy
    return 1 - cdist(a, B, metric='cosine')


def chunk(texts: list[str], batch_size: int) -> Generator[list[str], None, None]:
    logger.info(f"Truncating {len(texts)} texts")
    texts = [text[:25000] if len(text) > 25000 else text for text in texts]
    # remove empty string
    texts = [text if text else " " for text in texts]
    logger.info(f"Finished truncating {len(texts)} texts")
    for i in range(0, len(texts), batch_size):
        yield texts[i : i + batch_size] if i + batch_size < len(texts) else texts[i:]


# @file_cache(ignore_params=["texts"])
def multi_get_query_texts_similarity(queries: list[str], documents: list[str], cache_type: str = "main") -> list[float]:
    if not documents:
        return []
    embeddings = embed_text_array(documents, cache_type)
    embeddings = np.concatenate(embeddings)
    with Timer() as timer:
        query_embedding = np.array(openai_call_embedding(queries, input_type="query"))
    logger.info(f"Embedding query took {timer.time_elapsed:.2f} seconds")
    with Timer() as timer:
        similarity = cosine_similarity(query_embedding, embeddings)
    logger.info(f"Similarity took {timer.time_elapsed:.2f} seconds")
    similarity = similarity.tolist()
    return similarity


def normalize_l2(x):
    x = np.array(x)
    if x.ndim == 1:
        norm = np.linalg.norm(x)
        if norm == 0:
            return x
        return x / norm
    else:
        norm = np.linalg.norm(x, 2, axis=1, keepdims=True)
        return np.where(norm == 0, x, x / norm)

def batch_by_token_count_for_voyage(
    texts: list[str],
    max_tokens: int = 120_000,
    max_length: int = 128,
) -> list[list[str]]:
    """
    This function splits the texts into batches based on the token count.
    Max token count for Voyage is 120k and max batch length count is 128.
    """
    client = voyageai.Client()
    batches = []
    batch = []
    token_count = 0
    for text in texts:
        text_token_count = client.count_tokens([text])
        if token_count + text_token_count > max_tokens * 0.95 or len(batch) >= max_length:
            batches.append(batch)
            batch = [text]  # Start the new batch with the current text
            token_count = text_token_count  # Reset token count for the new batch
        else:
            batch.append(text)
            token_count += text_token_count
    if batch:
        batches.append(batch)
    del client
    return batches

# lru_cache(maxsize=20)
# @redis_cache()
def embed_text_array(texts: list[str], cache_type: str = "main") -> list[np.ndarray]:
    embeddings = []
    texts = [text if text else " " for text in texts]
    batches = [texts[i : i + BATCH_SIZE] for i in range(0, len(texts), BATCH_SIZE)]
    workers = min(max(1, multiprocessing.cpu_count() // 4), 1)
    with Timer() as timer:
        if workers > 1 and len(batches) > 1:
            with multiprocessing.Pool(
                processes=workers
            ) as pool:
                # 注意：multiprocessing 不能直接传递 cache_type 参数，需要使用 functools.partial
                from functools import partial
                embed_func = partial(openai_with_expo_backoff, cache_type=cache_type)
                embeddings = list(
                    tqdm(
                        pool.imap(embed_func, batches),
                        total=len(batches),
                        desc="openai embedding",
                    )
                )
        else:
            embeddings = [openai_with_expo_backoff(batch, cache_type) for batch in tqdm(batches, desc="openai embedding")]
    logger.info(f"Embedding docs took {timer.time_elapsed:.2f} seconds")
    return embeddings


# @redis_cache()
def openai_call_embedding_router(batch: list[str], input_type: str="document"): # input_type can be query or document
    # 这些变量已经从 config/server.py 导入，不需要重新定义

    if len(batch) == 0:
        return np.array([])

    # 优先使用 RedServing Embedding 服务
    # if CODEWIZ_RAG_ENABLED:
    try:
        headers = {
            'Content-Type': 'application/json',
            'api-key': LLM_API_KEY
        }

        def get_single_embedding(content):
            """获取单个内容的embedding"""
            payload = json.dumps({
                "model": "Qwen3-Embedding-0.6B",
                "input": content,
                "encoding_format": "float"
            })

            response = requests.post(CODEWIZ_RAG_URL, headers=headers, data=payload, timeout=30)
            response.raise_for_status()  # 如果请求失败，抛出异常

            result = response.json()
            embedding = result.get("data", [])[0].get("embedding", [])
            return embedding

        # 并发处理批量输入
        import concurrent.futures

        # 使用ThreadPoolExecutor进行并发请求，限制最大并发数为10
        with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:
            # 提交所有任务并保持顺序
            futures = [executor.submit(get_single_embedding, content) for content in batch]

            # 收集结果，保持原始顺序
            embeddings = []
            failed_count = 0
            for i, future in enumerate(futures):
                try:
                    embedding = future.result()
                    embeddings.append(embedding)
                except Exception as e:
                    logger.error(f"Failed to get embedding for content {i}: {str(e)}")
                    failed_count += 1
                    # 添加None作为占位符，而不是空列表
                    embeddings.append(None)

        # 如果所有请求都失败了，返回零向量数组
        if failed_count == len(batch):
            logger.warning("All RedServing embedding requests failed, returning zero vectors")
            # 返回形状为 (batch_size, 512) 的零向量数组
            return np.zeros((len(batch), 512))

        # 如果有部分成功，检查是否可以构建有效的数组
        valid_embeddings = [emb for emb in embeddings if emb is not None]
        if not valid_embeddings:
            logger.warning("RedServing returned no valid embeddings, returning zero vectors")
            # 返回形状为 (batch_size, 512) 的零向量数组
            return np.zeros((len(batch), 512))
        else:
            # 对于失败的embedding，用零向量填充
            embedding_dim = len(valid_embeddings[0]) if valid_embeddings else 512
            for i, emb in enumerate(embeddings):
                if emb is None:
                    embeddings[i] = np.zeros(embedding_dim)

            # 返回嵌入向量并进行 L2 归一化
            embeddings_array = np.array(embeddings)
            normalized_dim = normalize_l2(embeddings_array)
            return normalized_dim

    except Exception as e:
        logger.error(f"Error using RedServing embedding service: {e}")
        logger.info("Returning zero vectors due to service failure")
        # 返回形状为 (batch_size, 512) 的零向量数组
        return np.zeros((len(batch), 512))

    # if VOYAGE_API_USE_AWS:
    #     sm_runtime = boto3.client(
    #         "sagemaker-runtime",
    #         aws_access_key_id=VOYAGE_API_AWS_ACCESS_KEY,
    #         aws_secret_access_key=VOYAGE_API_AWS_SECRET_KEY,
    #         region_name=VOYAGE_API_AWS_REGION
    #     )
    #     input_json = json.dumps({
    #         "input": batch,
    #         "input_type": input_type,
    #         "truncation": "true"
    #     })
    #     response = sm_runtime.invoke_endpoint(
    #         EndpointName=VOYAGE_API_AWS_ENDPOINT_NAME,
    #         ContentType="application/json",
    #         Accept="application/json",
    #         Body=input_json,
    #     )
    #     body = response["Body"]
    #     obj = json.load(body)
    #     data = obj["data"]
    #     return np.array([vector["embedding"] for vector in data])
    # elif VOYAGE_API_KEY:
    #     client = voyageai.Client(api_key=VOYAGE_API_KEY)
    #     result = client.embed(batch, model="voyage-code-2", input_type=input_type, truncation=True)
    #     cut_dim = np.array([data for data in result.embeddings])
    #     normalized_dim = normalize_l2(cut_dim)
    #     del client
    #     return normalized_dim
    # else:
    #     client = get_embeddings_client()
    #     response = client.embeddings.create(
    #         input=batch, model="text-embedding-3-small", encoding_format="float"
    #     )
    #     cut_dim = np.array([data.embedding for data in response.data])[:, :512]
    #     normalized_dim = normalize_l2(cut_dim)
    #     # save results to redis
    #     return normalized_dim

def openai_call_embedding(batch: list[str], input_type: str="document"):
    # Backoff on batch size by splitting the batch in half.
    try:
        return openai_call_embedding_router(batch, input_type)
    except (voyageai_error.InvalidRequestError, ClientError) as e: # full error is botocore.errorfactory.ModelError: but I can't find it
        if len(batch) > 1 and "Please lower the number of tokens in the batch." in str(e):
            logger.error(f"Token count exceeded for batch: {max([tiktoken_client.count(text) for text in batch])} retrying by splitting batch in half.")
            mid = len(batch) // 2
            left = openai_call_embedding(batch[:mid], input_type)
            right = openai_call_embedding(batch[mid:], input_type)
            return np.concatenate((left, right))
        else:
            raise e
    except openai.BadRequestError as e:
        # In the future we can better handle this by averaging the embeddings of the split batch
        if "maximum context length" in str(e):
            logger.warning(f"Token count exceeded for batch: {max([tiktoken_client.count(text) for text in batch])} truncating down to 8192 tokens.")
            batch = [tiktoken_client.truncate_string(text) for text in batch]
            return openai_call_embedding(batch, input_type)


@backoff.on_exception(
    backoff.expo,
    requests.exceptions.Timeout,
    max_tries=5,
)
def openai_with_expo_backoff(batch: tuple[str], cache_type: str = "main"):
    # check cache first
    embeddings: list[np.ndarray | None] = [None] * len(batch)
    cache_keys = [hash_sha256(text) + CACHE_VERSION for text in batch]

    # 根据缓存类型选择对应的缓存实例
    current_vector_cache = get_vector_cache(cache_type)

    try:
        for i, cache_key in enumerate(cache_keys):
            cache_value = current_vector_cache.get(cache_key)
            if cache_value is not None:
                embeddings[i] = cache_value
    except Exception as e:
        logger.warning(f"Error reading embeddings from cache: {e}")

    # not stored in cache, call openai
    batch = [
        text for i, text in enumerate(batch) if embeddings[i] is None
    ]  # remove all the cached values from the batch
    if len(batch) == 0:
        embeddings = np.array(embeddings)
        return embeddings  # all embeddings are in cache
    try:
        # make sure all token counts are within model params (max: 8192)
        new_embeddings = openai_call_embedding(batch)
    except requests.exceptions.Timeout as e:
        logger.exception(f"Timeout error occured while embedding: {e}")
    except Exception as e:
        logger.exception(e)
        if any(tiktoken_client.count(text) > 8192 for text in batch):
            logger.warning(
                f"Token count exceeded for batch: {max([tiktoken_client.count(text) for text in batch])} truncating down to 8192 tokens."
            )
            batch = [tiktoken_client.truncate_string(text) for text in batch]
            new_embeddings = openai_call_embedding(batch)
        else:
            raise e
    # get all indices where embeddings are None
    indices = [i for i, emb in enumerate(embeddings) if emb is None]
    # store the new embeddings in the correct position
    assert len(indices) == len(new_embeddings)
    for i, index in enumerate(indices):
        embeddings[index] = new_embeddings[i]
    # store in cache
    try:
        for cache_key, embedding in zip(cache_keys, embeddings):
            current_vector_cache.set(cache_key, embedding)
        embeddings = np.array(embeddings)
    except Exception as e:
        logger.warning(f"Error storing embeddings in cache: {e}")
    return embeddings


if __name__ == "__main__":
    # 测试嵌入功能
    texts = ["sasxtt " * 10000 for _ in range(10)] + ["abb " * 1 for _ in range(10)]
    embeddings = embed_text_array(texts)
